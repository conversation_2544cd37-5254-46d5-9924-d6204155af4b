'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { apiService, DeveloperRegisterRequest, CreateApplicationRequest } from "@/lib/api";
import { CheckCircleIcon, AlertCircleIcon, CopyIcon } from "lucide-react";

interface DeveloperSetupProps {
  onAppIdCreated?: (appId: string) => void;
}

export function DeveloperSetup({ onAppIdCreated }: DeveloperSetupProps) {
  const [step, setStep] = useState<'register' | 'verify' | 'login' | 'create-app' | 'complete'>('register');
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState('');
  const [token, setToken] = useState('');
  const [appId, setAppId] = useState('');
  
  // Form data
  const [registerData, setRegisterData] = useState<DeveloperRegisterRequest>({
    name: '',
    email: '',
    password: ''
  });
  const [otp, setOtp] = useState('');
  const [loginData, setLoginData] = useState({
    email: '',
    password: ''
  });
  const [appData, setAppData] = useState<CreateApplicationRequest>({
    name: '',
    allowedDomains: ['localhost:3000', '127.0.0.1:3000']
  });

  const handleRegister = async () => {
    setIsLoading(true);
    setStatus('Registering developer account...');

    try {
      const response = await apiService.register(registerData);
      
      if (response.success) {
        setStatus('Registration successful! Please check your email for verification code.');
        setStep('verify');
        setLoginData({ email: registerData.email, password: registerData.password });
      } else {
        setStatus(response.error || 'Registration failed');
      }
    } catch (error) {
      setStatus('Network error during registration');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerify = async () => {
    setIsLoading(true);
    setStatus('Verifying account...');

    try {
      const response = await apiService.verify({ email: registerData.email, otp });
      
      if (response.success) {
        setStatus('Account verified successfully!');
        setStep('login');
      } else {
        setStatus(response.error || 'Verification failed');
      }
    } catch (error) {
      setStatus('Network error during verification');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async () => {
    setIsLoading(true);
    setStatus('Logging in...');

    try {
      const response = await apiService.login(loginData);
      
      if (response.success && response.data) {
        setToken(response.data.token);
        setStatus('Login successful!');
        setStep('create-app');
      } else {
        setStatus(response.error || 'Login failed');
      }
    } catch (error) {
      setStatus('Network error during login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateApp = async () => {
    setIsLoading(true);
    setStatus('Creating application...');

    try {
      const response = await apiService.createApplication(appData, token);
      
      if (response.success && response.data) {
        setAppId(response.data.appId);
        setStatus('Application created successfully!');
        setStep('complete');
        onAppIdCreated?.(response.data.appId);
      } else {
        setStatus(response.error || 'Application creation failed');
      }
    } catch (error) {
      setStatus('Network error during application creation');
    } finally {
      setIsLoading(false);
    }
  };

  const copyAppId = () => {
    navigator.clipboard.writeText(appId);
    setStatus('App ID copied to clipboard!');
  };

  const renderRegisterStep = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          value={registerData.name}
          onChange={(e) => setRegisterData({ ...registerData, name: e.target.value })}
          placeholder="Your name"
          disabled={isLoading}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={registerData.email}
          onChange={(e) => setRegisterData({ ...registerData, email: e.target.value })}
          placeholder="<EMAIL>"
          disabled={isLoading}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          value={registerData.password}
          onChange={(e) => setRegisterData({ ...registerData, password: e.target.value })}
          placeholder="Password"
          disabled={isLoading}
        />
      </div>
      <Button 
        onClick={handleRegister} 
        disabled={isLoading || !registerData.name || !registerData.email || !registerData.password}
        className="w-full"
      >
        {isLoading ? 'Registering...' : 'Register Developer Account'}
      </Button>
    </div>
  );

  const renderVerifyStep = () => (
    <div className="space-y-4">
      <div className="text-center">
        <p className="text-sm text-muted-foreground mb-4">
          Enter the 6-digit verification code sent to <strong>{registerData.email}</strong>
        </p>
      </div>
      <div className="space-y-2">
        <Label htmlFor="otp">Verification Code</Label>
        <Input
          id="otp"
          value={otp}
          onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
          placeholder="000000"
          maxLength={6}
          disabled={isLoading}
          className="text-center text-lg tracking-widest"
        />
      </div>
      <Button 
        onClick={handleVerify} 
        disabled={isLoading || otp.length !== 6}
        className="w-full"
      >
        {isLoading ? 'Verifying...' : 'Verify Account'}
      </Button>
    </div>
  );

  const renderLoginStep = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="login-email">Email</Label>
        <Input
          id="login-email"
          type="email"
          value={loginData.email}
          onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
          placeholder="<EMAIL>"
          disabled={isLoading}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="login-password">Password</Label>
        <Input
          id="login-password"
          type="password"
          value={loginData.password}
          onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
          placeholder="Password"
          disabled={isLoading}
        />
      </div>
      <Button 
        onClick={handleLogin} 
        disabled={isLoading || !loginData.email || !loginData.password}
        className="w-full"
      >
        {isLoading ? 'Logging in...' : 'Login'}
      </Button>
    </div>
  );

  const renderCreateAppStep = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="app-name">Application Name</Label>
        <Input
          id="app-name"
          value={appData.name}
          onChange={(e) => setAppData({ ...appData, name: e.target.value })}
          placeholder="My Crefy Connect App"
          disabled={isLoading}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="domains">Allowed Domains</Label>
        <Input
          id="domains"
          value={appData.allowedDomains.join(', ')}
          onChange={(e) => setAppData({ ...appData, allowedDomains: e.target.value.split(',').map(d => d.trim()) })}
          placeholder="localhost:3000, yourdomain.com"
          disabled={isLoading}
        />
      </div>
      <Button 
        onClick={handleCreateApp} 
        disabled={isLoading || !appData.name}
        className="w-full"
      >
        {isLoading ? 'Creating...' : 'Create Application'}
      </Button>
    </div>
  );

  const renderCompleteStep = () => (
    <div className="space-y-4 text-center">
      <CheckCircleIcon className="mx-auto h-12 w-12 text-green-500" />
      <h3 className="text-lg font-semibold">Setup Complete!</h3>
      <p className="text-sm text-muted-foreground">
        Your application has been created successfully.
      </p>
      <div className="flex items-center justify-center space-x-2">
        <Badge variant="secondary" className="font-mono">
          {appId}
        </Badge>
        <Button size="sm" variant="outline" onClick={copyAppId}>
          <CopyIcon className="h-4 w-4" />
        </Button>
      </div>
      <p className="text-xs text-muted-foreground">
        Use this App ID for OAuth authentication
      </p>
    </div>
  );

  const getStepTitle = () => {
    switch (step) {
      case 'register': return 'Register Developer Account';
      case 'verify': return 'Verify Account';
      case 'login': return 'Login';
      case 'create-app': return 'Create Application';
      case 'complete': return 'Setup Complete';
      default: return 'Developer Setup';
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>{getStepTitle()}</span>
          {step === 'complete' && <CheckCircleIcon className="h-5 w-5 text-green-500" />}
        </CardTitle>
        <CardDescription>
          Set up your developer account and create an application for OAuth integration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {step === 'register' && renderRegisterStep()}
        {step === 'verify' && renderVerifyStep()}
        {step === 'login' && renderLoginStep()}
        {step === 'create-app' && renderCreateAppStep()}
        {step === 'complete' && renderCompleteStep()}

        {status && (
          <Alert className={status.includes('successful') || status.includes('copied') ? 'border-green-200' : 'border-orange-200'}>
            {status.includes('successful') || status.includes('copied') ? (
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircleIcon className="h-4 w-4 text-orange-600" />
            )}
            <AlertDescription>{status}</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
