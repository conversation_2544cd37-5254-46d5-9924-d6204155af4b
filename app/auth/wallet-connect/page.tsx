'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { SocialWalletAuth } from '@/components/auth/social-wallet-auth';
import { WalletData } from '@/lib/api';
import { useAuth } from '@/lib/auth-context';
import { toast } from 'sonner';

export default function WalletConnectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const [appId, setAppId] = useState<string>('');

  useEffect(() => {
    // Get appId from URL params or use a default one
    const urlAppId = searchParams.get('appId');
    if (urlAppId) {
      setAppId(urlAppId);
    } else {
      // For demo purposes, use a default app ID
      // In production, this should be provided by the application
      setAppId('demo_app_id');
    }
  }, [searchParams]);

  const handleWalletSuccess = (walletData: WalletData, token: string) => {
    try {
      // Parse user data from wallet
      const userData = JSON.parse(walletData.userData);
      
      // Create user object for auth context
      const user = {
        id: walletData.walletAddress,
        email: userData.email,
        name: userData.name || '',
        isVerified: true,
        createdAt: new Date().toISOString(),
      };

      // Login with wallet data
      login(user, token, 'wallet-user');
      
      toast.success('Successfully connected to your social wallet!');
      
      // Redirect to dashboard
      setTimeout(() => {
        router.push('/dashboard/dashboard');
      }, 1000);
      
    } catch (error) {
      console.error('Error processing wallet data:', error);
      toast.error('Error processing wallet connection');
    }
  };

  const handleWalletError = (error: string) => {
    console.error('Wallet authentication error:', error);
    toast.error('Failed to connect wallet: ' + error);
  };

  if (!appId) {
    return (
      <main className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-blue-50 p-4">
      {/* Background Pattern */}
      <div 
        className="absolute inset-0 opacity-5 bg-repeat"
        style={{ 
          backgroundImage: "url('data:image/svg+xml,<svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"none\" fill-rule=\"evenodd\"><g fill=\"%23000\" fill-opacity=\"0.1\"><circle cx=\"30\" cy=\"30\" r=\"4\"/></g></svg>')" 
        }}
      />
      
      {/* Main Content */}
      <div className="relative z-10 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-2">
            Crefy Connect
          </h1>
          <p className="text-muted-foreground">
            Connect your social wallet to access Web3 features
          </p>
        </div>

        <SocialWalletAuth
          appId={appId}
          onSuccess={handleWalletSuccess}
          onError={handleWalletError}
          className="backdrop-blur-sm bg-white/80 border border-gray-200 shadow-xl"
        />

        <div className="text-center mt-6 space-y-2">
          <p className="text-sm text-muted-foreground">
            Need help?{' '}
            <a 
              href="/docs" 
              className="text-[#4A148C] hover:underline font-medium"
            >
              View Documentation
            </a>
          </p>
          <p className="text-xs text-muted-foreground">
            Powered by Crefy Connect Social Wallet Authentication
          </p>
        </div>
      </div>
    </main>
  );
}
